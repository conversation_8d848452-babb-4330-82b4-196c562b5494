/**
 * Classe modèle pour les réservations
 */
class Reservation {
    constructor(id, dateTime, status, clientId, tableId) {
        this.id = id;
        this.dateTime = dateTime;
        this.status = status;
        this.clientId = clientId;
        this.tableId = tableId;
    }

    // Méthode pour vérifier la disponibilité des tables
    static async checkAvailability(date, time, seats) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=checkAvailability', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ date, time, seats })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la vérification de disponibilité:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour créer une nouvelle réservation
    static async createReservation(clientId, tableId, date, time) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=createReservation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId, tableId, date, time })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la création de la réservation:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour obtenir les réservations d'un client
    static async getClientReservations(clientId) {
        try {
            const response = await fetch(`http://restaurantmila.atwebpages.com/sever/reservation.php?action=getClientReservations&clientId=${clientId}`);
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération des réservations:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour mettre à jour une réservation
    static async updateReservation(id, tableId, date, time) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=updateReservation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id, tableId, date, time })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la mise à jour de la réservation:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour annuler une réservation
    static async cancelReservation(id) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=cancelReservation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de l\'annulation de la réservation:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }
}