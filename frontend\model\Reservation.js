/**
 * Classe modèle pour les réservations
 */
class Reservation {
    constructor(id, dateTime, status, clientId, tableId) {
        this.id = id;
        this.dateTime = dateTime;
        this.status = status;
        this.clientId = clientId;
        this.tableId = tableId;
    }

    // Méthode pour vérifier la disponibilité des tables
    static async checkAvailability(date, time, seats) {
        try {
            const response = await fetch('../../backend/server/reservation.php?action=check_availability', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ date, time, seats })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la vérification de disponibilité:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour créer une nouvelle réservation
    static async createReservation(clientId, tableId, date, time) {
        try {
            const response = await fetch('../../backend/server/reservation.php?action=create_reservation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId, tableId, date, time })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la création de la réservation:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour obtenir les réservations d'un client
    static async getClientReservations(clientId) {
        try {
            const response = await fetch('../../backend/server/reservation.php?action=get_client_reservations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ client_id: clientId })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération des réservations:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour mettre à jour une réservation
    static async updateReservation(id, tableId, date, time) {
        try {
            const response = await fetch('../../backend/server/reservation.php?action=update_reservation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ reservation_id: id, table_id: tableId, date, time, status: 'Confirmed' })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la mise à jour de la réservation:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour annuler une réservation
    static async cancelReservation(id) {
        try {
            const response = await fetch('../../backend/server/reservation.php?action=cancel_reservation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ reservation_id: id })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de l\'annulation de la réservation:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }
}