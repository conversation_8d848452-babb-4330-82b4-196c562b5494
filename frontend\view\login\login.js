document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('login-form');
    const messageDiv = document.getElementById('message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Récupérer les valeurs du formulaire
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        
        // Validation côté client
        if (!email || !password) {
            showMessage('Veuillez remplir tous les champs.', 'error');
            return;
        }
        
        // Validation de l'email avec une expression régulière
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showMessage('Veuillez entrer une adresse email valide.', 'error');
            return;
        }
        
        // Préparation des données pour l'API
        const userData = {
            email: email,
            password: password
        };
        
        // Envoi des données à l'API
        fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                
                // Stocker les informations de l'utilisateur dans le localStorage
                localStorage.setItem('user', JSON.stringify(data.user));
                
                // Redirection en fonction du rôle de l'utilisateur
                setTimeout(() => {
                    switch(data.user.role) {
                        case 'Admin':
                            window.location.href = '../admin/dashboard.html';
                            break;
                        case 'Manager':
                            window.location.href = '../manager/dashboard.html';
                            break;
                        case 'Client':
                        default:
                            window.location.href = '../client/dashboard.html';
                            break;
                    }
                }, 1000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showMessage('Une erreur est survenue lors de la communication avec le serveur.', 'error');
        });
    });
    
    // Fonction pour afficher les messages
    function showMessage(text, type) {
        messageDiv.textContent = text;
        messageDiv.className = 'message ' + type;
        
        // Faire défiler jusqu'au message
        messageDiv.scrollIntoView({ behavior: 'smooth' });
    }
});