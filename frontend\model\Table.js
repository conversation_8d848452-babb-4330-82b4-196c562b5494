/**
 * Classe modèle pour les tables du restaurant
 */
class Table {
    constructor(id, seats, status) {
        this.id = id;
        this.seats = seats;
        this.status = status;
    }

    // Méthode pour obtenir toutes les tables
    static async getAllTables() {
        try {
            const response = await fetch('../../backend/server/table.php?action=getAllTables');
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération des tables:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour obtenir une table par son ID
    static async getTableById(id) {
        try {
            const response = await fetch(`../../backend/server/table.php?action=getTableById&id=${id}`);
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération de la table:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour obtenir les tables disponibles par nombre de places
    static async getAvailableTablesBySeats(seats, date, time) {
        try {
            const response = await fetch('../../backend/server/reservation.php?action=check_availability', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ seats, date, time })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération des tables disponibles:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }
}