document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('signup-form');
    const messageDiv = document.getElementById('message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Récupérer les valeurs du formulaire
        const name = document.getElementById('name').value.trim();
        const email = document.getElementById('email').value.trim();
        const phone = document.getElementById('phone').value.trim();
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        
        // Validation côté client
        if (!name || !email || !phone || !password || !confirmPassword) {
            showMessage('Veuillez remplir tous les champs.', 'error');
            return;
        }
        
        if (password !== confirmPassword) {
            showMessage('Les mots de passe ne correspondent pas.', 'error');
            return;
        }
        
        // Validation de l'email avec une expression régulière
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showMessage('Veuillez entrer une adresse email valide.', 'error');
            return;
        }
        
        // Validation du numéro de téléphone (format français)
        const phoneRegex = /^(0|\+33)[1-9]([-. ]?[0-9]{2}){4}$/;
        if (!phoneRegex.test(phone)) {
            showMessage('Veuillez entrer un numéro de téléphone valide.', 'error');
            return;
        }
        
        // Préparation des données pour l'API
        const userData = {
            name: name,
            email: email,
            phone: phone,
            password: password
        };
        console.log(userData);
        // Envoi des données à l'API
        fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(data);
                showMessage(data.message, 'success');
                form.reset();
                // Redirection vers la page de connexion après 2 secondes
                setTimeout(() => {
                    window.location.href = '../login/login.html';
                }, 2000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showMessage('Une erreur est survenue lors de la communication avec le serveur.', 'error');
        });
    });
    
    // Fonction pour afficher les messages
    function showMessage(text, type) {
        messageDiv.textContent = text;
        messageDiv.className = 'message ' + type;
        
        // Faire défiler jusqu'au message
        messageDiv.scrollIntoView({ behavior: 'smooth' });
    }
});