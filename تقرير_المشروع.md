# تقرير مشروع نظام حجز المطعم

## مقدمة

هذا التقرير يوثق مشروع نظام حجز المطعم الذي تم تطويره باستخدام تقنيات الويب الأساسية. يهدف المشروع إلى إنشاء تطبيق ويب متكامل لإدارة حجوزات المطعم، حيث يمكن للمستخدمين التسجيل وتسجيل الدخول وعرض توفر الطاولات وإجراء الحجوزات وإدارتها، بينما يمكن للمديرين والمسؤولين الإشراف على الطاولات وعناصر القائمة وحسابات المستخدمين.

## هيكل المشروع

يتبع المشروع نمط التصميم MVC (النموذج-العرض-المتحكم) مع فصل واضح بين طبقات التطبيق:

### هيكل المجلدات

```
project-root/
├── frontend/
│   ├── model/
│   │   └── (تحتوي على تعريفات الفئات)
│   ├── view/
│   │   ├── signup/         
│   │   │   ├── signup.html
│   │   │   ├── signup.css
│   │   │   └── signup.js
│   │   ├── login/
│   │   │   ├── login.html
│   │   │   └── login.js
│   │   └── ... (مجلدات أخرى لحالات الاستخدام)
│   └── controller/
│       ├── signupController.js
│       ├── loginController.js
│       └── ... (متحكمات منطق الأعمال الأخرى)

├── backend/
│   ├── database.sql        
│   └── server/
│       ├── database.php    (اتصال قاعدة البيانات)
│       ├── user.php        (واجهة برمجة التطبيقات المتعلقة بالمستخدم)
│       └── reservation.php (واجهة برمجة التطبيقات المتعلقة بالحجز)
```

## النموذج العلائقي لقاعدة البيانات

تم تصميم قاعدة البيانات باستخدام النموذج العلائقي التالي:

- **User**: (id, email, password, role, status)
- **Client**: (id, name, phone, user_id)
- **Reservation**: (id, date_time, status, client_id, table_id)
- **Table**: (id, seats, status)
- **Menu**: (id, name)
- **Dish**: (id, name, description, price, menu_id)
- **Payment**: (id, reservation_id, amount, date, method, status)

## التقنيات المستخدمة

تم تطوير المشروع باستخدام التقنيات التالية:

- **الواجهة الأمامية (Frontend)**:
  - HTML: لهيكلة صفحات الويب
  - CSS: لتنسيق وتصميم الصفحات
  - JavaScript: للتفاعل مع المستخدم والاتصال بالخادم

- **الواجهة الخلفية (Backend)**:
  - PHP: لمعالجة طلبات الخادم وتنفيذ منطق الأعمال
  - MySQL: لتخزين واسترجاع البيانات

## الوظائف الرئيسية

### 1. إدارة المستخدمين

- **التسجيل**: يمكن للمستخدمين إنشاء حساب جديد بتقديم البريد الإلكتروني وكلمة المرور والاسم ورقم الهاتف.
- **تسجيل الدخول**: يمكن للمستخدمين تسجيل الدخول باستخدام بريدهم الإلكتروني وكلمة المرور.
- **إدارة الملف الشخصي**: يمكن للمستخدمين تحديث معلوماتهم الشخصية وتغيير كلمة المرور.

### 2. إدارة الحجوزات

- **التحقق من التوفر**: يمكن للعملاء التحقق من توفر الطاولات بناءً على التاريخ والوقت وعدد الأشخاص.
- **إنشاء حجز**: يمكن للعملاء حجز طاولة متاحة.
- **تعديل الحجز**: يمكن للعملاء تعديل تفاصيل الحجز الموجود (التاريخ، الوقت، الطاولة).
- **إلغاء الحجز**: يمكن للعملاء إلغاء حجز موجود.

### 3. إدارة النظام (للمديرين والمسؤولين)

- **إدارة المستخدمين**: يمكن للمسؤولين عرض وتعديل وتعطيل حسابات المستخدمين.
- **إدارة الطاولات**: يمكن للمديرين إضافة وتعديل وإزالة الطاولات.
- **إدارة الحجوزات**: يمكن للمديرين عرض وتعديل جميع الحجوزات.
- **إدارة القائمة**: يمكن للمديرين إدارة القوائم والأطباق.

## تنفيذ واجهة برمجة التطبيقات (API)

### 1. واجهة برمجة تطبيقات المستخدم (user.php)

توفر هذه الواجهة الوظائف التالية:

- `register`: لتسجيل مستخدم جديد
- `login`: لمصادقة المستخدم
- `logout`: لتسجيل خروج المستخدم
- `getUserInfo`: للحصول على معلومات المستخدم
- `updateClientInfo`: لتحديث معلومات العميل
- `changePassword`: لتغيير كلمة مرور المستخدم
- `getAllUsers`: للحصول على قائمة جميع المستخدمين (للمسؤولين)
- `changeUserStatus`: لتغيير حالة المستخدم (تنشيط/تعطيل)
- `changeUserRole`: لتغيير دور المستخدم

### 2. واجهة برمجة تطبيقات الحجز (reservation.php)

توفر هذه الواجهة الوظائف التالية:

- `checkAvailability`: للتحقق من توفر الطاولات
- `createReservation`: لإنشاء حجز جديد
- `getReservation`: للحصول على تفاصيل حجز
- `getClientReservations`: للحصول على جميع حجوزات عميل معين
- `updateReservation`: لتحديث حجز موجود
- `cancelReservation`: لإلغاء حجز
- `getAllReservations`: للحصول على جميع الحجوزات (للمديرين)
- `getReservationsByDate`: للحصول على الحجوزات حسب التاريخ

## واجهة المستخدم

تم تصميم واجهة المستخدم لتكون سهلة الاستخدام وجذابة بصريًا:

### 1. الصفحة الرئيسية

تعرض الصفحة الرئيسية معلومات عن المطعم وروابط للتسجيل وتسجيل الدخول وحجز طاولة.

### 2. صفحة التسجيل

تتيح للمستخدمين إنشاء حساب جديد عن طريق تقديم المعلومات المطلوبة.

### 3. صفحة تسجيل الدخول

تتيح للمستخدمين تسجيل الدخول إلى حساباتهم.

### 4. لوحة تحكم العميل

تعرض معلومات العميل والحجوزات الحالية والسابقة، وتوفر خيارات لإنشاء وتعديل وإلغاء الحجوزات.

### 5. لوحة تحكم المدير/المسؤول

توفر واجهة لإدارة المستخدمين والطاولات والحجوزات والقوائم.

## الأمان

تم تنفيذ تدابير الأمان التالية:

- **تشفير كلمة المرور**: يتم تخزين كلمات المرور بشكل مشفر باستخدام خوارزمية التجزئة.
- **التحقق من صحة المدخلات**: يتم التحقق من جميع مدخلات المستخدم على جانبي العميل والخادم.
- **التحكم في الوصول**: يتم التحقق من أذونات المستخدم قبل السماح بالوصول إلى الوظائف المقيدة.
- **جلسات آمنة**: يتم استخدام جلسات PHP لإدارة حالة المستخدم بشكل آمن.

## الاستنتاج

يوفر نظام حجز المطعم حلاً شاملاً لإدارة حجوزات المطعم، مع واجهة مستخدم سهلة الاستخدام وواجهة خلفية قوية. يمكن تخصيص النظام وتوسيعه بسهولة لتلبية متطلبات محددة للمطعم.

## الخطوات المستقبلية

- **تنفيذ نظام الدفع**: إضافة وظائف لمعالجة المدفوعات عبر الإنترنت.
- **تكامل البريد الإلكتروني**: إرسال تأكيدات وتذكيرات الحجز عبر البريد الإلكتروني.
- **تطبيق الهاتف المحمول**: تطوير تطبيق للهواتف الذكية للوصول السهل.
- **تحسينات واجهة المستخدم**: تحسين تجربة المستخدم بناءً على التعليقات.
- **تحسين الأداء**: تحسين أداء النظام للتعامل مع المزيد من المستخدمين والحجوزات.

## ملاحظات التنفيذ

- تم تطوير المشروع باستخدام اللغة الفرنسية لواجهة المستخدم والتعليقات البرمجية، وفقًا للمتطلبات.
- تم تصميم النظام ليكون قابلاً للنشر على منصة awardspace، مع إمكانية تغيير عناوين URL بسهولة.
- تم استخدام تقنيات الويب الأساسية فقط (HTML، CSS، JavaScript، PHP، MySQL) كما هو مطلوب.

---

تم إعداد هذا التقرير كجزء من توثيق مشروع نظام حجز المطعم. يرجى الرجوع إلى التعليقات البرمجية والوثائق الفنية للحصول على مزيد من التفاصيل حول التنفيذ.