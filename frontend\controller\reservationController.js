/**
 * Contr<PERSON>leur pour gérer les opérations liées aux réservations
 */
class ReservationController {
    /**
     * Vérifie la disponibilité des tables
     * @param {string} date - La date de réservation
     * @param {string} time - L'heure de réservation
     * @param {number} seats - Le nombre de places nécessaires
     * @returns {Promise} - Résultat de la vérification
     */
    static async checkAvailability(date, time, seats) {
        try {
            // Validation des entrées
            if (!date || !time || !seats) {
                return { success: false, message: 'Veuillez remplir tous les champs.' };
            }
            
            // Appel au modèle pour vérifier la disponibilité
            const result = await Reservation.checkAvailability(date, time, seats);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la vérification.' };
        }
    }
    
    /**
     * Crée une nouvelle réservation
     * @param {number} clientId - L'ID du client
     * @param {number} tableId - L'ID de la table
     * @param {string} date - La date de réservation
     * @param {string} time - L'heure de réservation
     * @returns {Promise} - Résultat de la création
     */
    static async createReservation(clientId, tableId, date, time) {
        try {
            console.log('createReservation', clientId, tableId, date, time);
            // Validation des entrées
            if (!clientId || !tableId || !date || !time) {
                return { success: false, message: 'Informations de réservation incomplètes.' };
            }
            
            // Appel au modèle pour créer la réservation
            const result = await Reservation.createReservation(clientId, tableId, date, time);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la création de la réservation.' };
        }
    }
    
    /**
     * Récupère les réservations d'un client
     * @param {number} clientId - L'ID du client
     * @returns {Promise} - Liste des réservations
     */
    static async getClientReservations(clientId) {
        try {
            if (!clientId) {
                return { success: false, message: 'ID client non spécifié.' };
            }
            
            const result = await Reservation.getClientReservations(clientId);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la récupération des réservations.' };
        }
    }
    
    /**
     * Met à jour une réservation existante
     * @param {number} id - L'ID de la réservation
     * @param {number} tableId - L'ID de la nouvelle table
     * @param {string} date - La nouvelle date
     * @param {string} time - La nouvelle heure
     * @returns {Promise} - Résultat de la mise à jour
     */
    static async updateReservation(id, tableId, date, time) {
        try {
            // Validation des entrées
            if (!id || !tableId || !date || !time) {
                return { success: false, message: 'Informations de mise à jour incomplètes.' };
            }
            
            // Appel au modèle pour mettre à jour la réservation
            const result = await Reservation.updateReservation(id, tableId, date, time);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la mise à jour de la réservation.' };
        }
    }
    
    /**
     * Annule une réservation
     * @param {number} id - L'ID de la réservation
     * @returns {Promise} - Résultat de l'annulation
     */
    static async cancelReservation(id) {
        try {
            if (!id) {
                return { success: false, message: 'ID de réservation non spécifié.' };
            }
            
            const result = await Reservation.cancelReservation(id);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de l\'annulation de la réservation.' };
        }
    }
}