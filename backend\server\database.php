<?php
// Paramètres de connexion à la base de données
$host = 'localhost'; // À modifier lors du déploiement sur awardspace
$dbname = 'restaurant_reservation';
$username = 'root'; // À modifier lors du déploiement
$password = ''; // À modifier lors du déploiement

// Fonction pour établir une connexion à la base de données
function connectDB() {
    global $host, $dbname, $username, $password;
    
    try {
        $conn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
        // Configuration des erreurs PDO
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        // En production, il serait préférable de logger l'erreur plutôt que de l'afficher
        die("Erreur de connexion à la base de données: " . $e->getMessage());
    }
}
?>