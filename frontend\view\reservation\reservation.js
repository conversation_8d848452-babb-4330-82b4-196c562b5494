document.addEventListener('DOMContentLoaded', function() {
    // Éléments du DOM
    const availabilityForm = document.getElementById('availability-form');
    const availabilitySection = document.getElementById('availability-section');
    const tablesSection = document.getElementById('tables-section');
    const confirmationSection = document.getElementById('confirmation-section');
    const manageReservationsSection = document.getElementById('manage-reservations-section');
    const tablesList = document.getElementById('tables-list');
    const availabilityMessage = document.getElementById('availability-message');
    const confirmationMessage = document.getElementById('confirmation-message');
    const reservationsList = document.getElementById('reservations-list');
    const editModal = document.getElementById('edit-modal');
    const editForm = document.getElementById('edit-form');
    const editMessage = document.getElementById('edit-message');
    const editTablesList = document.getElementById('edit-tables-list');
    
    // Boutons de navigation
    const backToAvailabilityBtn = document.getElementById('back-to-availability');
    const backToTablesBtn = document.getElementById('back-to-tables');
    const confirmReservationBtn = document.getElementById('confirm-reservation');
    const viewReservationsBtn = document.getElementById('view-reservations');
    const backToMainBtn = document.getElementById('back-to-main');
    
    // Variables pour stocker les données de réservation
    let selectedDate = '';
    let selectedTime = '';
    let selectedSeats = 0;
    let selectedTableId = null;
    let availableTables = [];
    let clientId = null;
    let currentReservationId = null;
    
    // Définir la date minimale à aujourd'hui
    const dateInput = document.getElementById('date');
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    dateInput.min = formattedDate;
    dateInput.value = formattedDate;
    
    // Vérifier si l'utilisateur est connecté
    function checkUserLoggedIn() {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user) {
            window.location.href = '../login/login.html';
            return false;
        }
        clientId = user.clientId;
        return true;
    }
    
    // Initialisation
    function init() {
        if (!checkUserLoggedIn()) return;
        
        // Afficher les réservations si demandé
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('view') === 'reservations') {
            showManageReservations();
        }
    }
    
    // Afficher un message
    function showMessage(element, message, type) {
        element.textContent = message;
        element.className = 'message ' + type;
        element.style.display = 'block';
    }
    
    // Vérifier la disponibilité des tables
    availabilityForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!checkUserLoggedIn()) return;
        
        selectedDate = document.getElementById('date').value;
        selectedTime = document.getElementById('time').value;
        selectedSeats = document.getElementById('seats').value;
        
        // Vérifier que tous les champs sont remplis
        if (!selectedDate || !selectedTime || !selectedSeats) {
            showMessage(availabilityMessage, 'Veuillez remplir tous les champs.', 'error');
            return;
        }
        
        try {
            // Appel au contrôleur pour vérifier la disponibilité
            const result = await ReservationController.checkAvailability(selectedDate, selectedTime, selectedSeats);
            
            if (result.success && result.tables && result.tables.length > 0) {
                availableTables = result.tables;
                displayAvailableTables(result.tables);
                availabilitySection.classList.add('hidden');
                tablesSection.classList.remove('hidden');
            } else {
                showMessage(availabilityMessage, result.message || 'Aucune table disponible pour cette date et cette heure.', 'error');
            }
        } catch (error) {
            console.error('Erreur:', error);
            showMessage(availabilityMessage, 'Une erreur est survenue lors de la vérification.', 'error');
        }
    });
    
    // Afficher les tables disponibles
    function displayAvailableTables(tables) {
        tablesList.innerHTML = '';
        
        tables.forEach(table => {
            const tableCard = document.createElement('div');
            tableCard.className = 'table-card';
            tableCard.dataset.tableId = table.id;
            tableCard.innerHTML = `
                <h3>Table ${table.id}</h3>
                <p>${table.seats} places</p>
            `;
            
            tableCard.addEventListener('click', function() {
                // Désélectionner toutes les tables
                document.querySelectorAll('.table-card').forEach(card => {
                    card.classList.remove('selected');
                });
                
                // Sélectionner cette table
                this.classList.add('selected');
                selectedTableId = table.id;
                
                // Mettre à jour les détails de confirmation
                document.getElementById('confirm-date').textContent = selectedDate;
                document.getElementById('confirm-time').textContent = selectedTime;
                document.getElementById('confirm-seats').textContent = selectedSeats;
                document.getElementById('confirm-table').textContent = `Table ${selectedTableId} (${table.seats} places)`;
                
                // Afficher la section de confirmation
                tablesSection.classList.add('hidden');
                confirmationSection.classList.remove('hidden');
            });
            
            tablesList.appendChild(tableCard);
        });
    }
    
    // Confirmer la réservation
    confirmReservationBtn.addEventListener('click', async function() {
        if (!checkUserLoggedIn()) return;
        
        if (!selectedTableId) {
            showMessage(confirmationMessage, 'Veuillez sélectionner une table.', 'error');
            return;
        }
        
        try {
            // Appel au contrôleur pour créer la réservation
            const result = await ReservationController.createReservation(clientId, selectedTableId, selectedDate, selectedTime);
            
            if (result.success) {
                showMessage(confirmationMessage, result.message || 'Réservation confirmée avec succès!', 'success');
                
                // Rediriger vers la gestion des réservations après un délai
                setTimeout(() => {
                    showManageReservations();
                }, 2000);
            } else {
                showMessage(confirmationMessage, result.message || 'Erreur lors de la réservation.', 'error');
            }
        } catch (error) {
            console.error('Erreur:', error);
            showMessage(confirmationMessage, 'Une erreur est survenue lors de la réservation.', 'error');
        }
    });
    
    // Afficher la gestion des réservations
    async function showManageReservations() {
        if (!checkUserLoggedIn()) return;
        
        // Cacher toutes les sections et afficher la section de gestion
        availabilitySection.classList.add('hidden');
        tablesSection.classList.add('hidden');
        confirmationSection.classList.add('hidden');
        manageReservationsSection.classList.remove('hidden');
        
        try {
            // Appel au contrôleur pour récupérer les réservations du client
            const result = await ReservationController.getClientReservations(clientId);
            
            if (result.success && result.reservations) {
                displayReservations(result.reservations);
            } else {
                reservationsList.innerHTML = '<p>Aucune réservation trouvée.</p>';
            }
        } catch (error) {
            console.error('Erreur:', error);
            reservationsList.innerHTML = '<p>Erreur lors de la récupération des réservations.</p>';
        }
    }
    
    // Afficher les réservations
    function displayReservations(reservations) {
        reservationsList.innerHTML = '';
        
        if (reservations.length === 0) {
            reservationsList.innerHTML = '<p>Aucune réservation trouvée.</p>';
            return;
        }
        
        reservations.forEach(reservation => {
            const dateTime = new Date(reservation.date_time);
            const formattedDate = dateTime.toLocaleDateString('fr-FR');
            const formattedTime = dateTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
            
            const reservationItem = document.createElement('div');
            reservationItem.className = 'reservation-item';
            reservationItem.innerHTML = `
                <h3>Réservation #${reservation.id}</h3>
                <p><strong>Date:</strong> ${formattedDate}</p>
                <p><strong>Heure:</strong> ${formattedTime}</p>
                <p><strong>Table:</strong> Table ${reservation.table_id}</p>
                <p><strong>Statut:</strong> ${getStatusLabel(reservation.status)}</p>
                <div class="actions">
                    ${reservation.status !== 'Cancelled' ? `
                        <button class="btn-primary edit-btn" data-id="${reservation.id}">Modifier</button>
                        <button class="btn-secondary cancel-btn" data-id="${reservation.id}">Annuler</button>
                    ` : ''}
                </div>
            `;
            
            reservationsList.appendChild(reservationItem);
        });
        
        // Ajouter les écouteurs d'événements pour les boutons
        document.querySelectorAll('.edit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const reservationId = this.dataset.id;
                openEditModal(reservationId, reservations);
            });
        });
        
        document.querySelectorAll('.cancel-btn').forEach(button => {
            button.addEventListener('click', function() {
                const reservationId = this.dataset.id;
                cancelReservation(reservationId);
            });
        });
    }
    
    // Obtenir le libellé du statut
    function getStatusLabel(status) {
        switch (status) {
            case 'Pending': return 'En attente';
            case 'Confirmed': return 'Confirmée';
            case 'Cancelled': return 'Annulée';
            case 'Completed': return 'Terminée';
            default: return status;
        }
    }
    
    // Ouvrir la modal de modification
    async function openEditModal(reservationId, reservations) {
        currentReservationId = reservationId;
        
        // Trouver la réservation correspondante
        const reservation = reservations.find(r => r.id == reservationId);
        if (!reservation) return;
        
        // Remplir le formulaire avec les données actuelles
        const dateTime = new Date(reservation.date_time);
        const formattedDate = dateTime.toISOString().split('T')[0];
        const formattedTime = dateTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        
        document.getElementById('edit-reservation-id').value = reservationId;
        document.getElementById('edit-date').value = formattedDate;
        document.getElementById('edit-date').min = today.toISOString().split('T')[0];
        
        // Sélectionner l'heure dans le select
        const timeSelect = document.getElementById('edit-time');
        for (let i = 0; i < timeSelect.options.length; i++) {
            if (timeSelect.options[i].value === formattedTime.substring(0, 5)) {
                timeSelect.selectedIndex = i;
                break;
            }
        }
        
        // Vérifier la disponibilité des tables pour la nouvelle date/heure
        try {
            const result = await ReservationController.checkAvailability(
                formattedDate, 
                formattedTime.substring(0, 5), 
                1 // Le nombre de places n'est pas important ici
            );
            
            if (result.success && result.tables) {
                // Ajouter la table actuelle à la liste si elle n'y est pas déjà
                const currentTableExists = result.tables.some(t => t.id == reservation.table_id);
                if (!currentTableExists) {
                    result.tables.push({
                        id: reservation.table_id,
                        seats: 'Actuelle'
                    });
                }
                
                displayEditTables(result.tables, reservation.table_id);
            }
        } catch (error) {
            console.error('Erreur:', error);
        }
        
        // Afficher la modal
        editModal.classList.add('show');
    }
    
    // Afficher les tables disponibles pour la modification
    function displayEditTables(tables, currentTableId) {
        editTablesList.innerHTML = '';
        
        tables.forEach(table => {
            const tableCard = document.createElement('div');
            tableCard.className = 'table-card';
            tableCard.dataset.tableId = table.id;
            
            if (table.id == currentTableId) {
                tableCard.classList.add('selected');
            }
            
            tableCard.innerHTML = `
                <h3>Table ${table.id}</h3>
                <p>${table.seats} places</p>
                ${table.id == currentTableId ? '<p><em>(Table actuelle)</em></p>' : ''}
            `;
            
            tableCard.addEventListener('click', function() {
                // Désélectionner toutes les tables
                document.querySelectorAll('#edit-tables-list .table-card').forEach(card => {
                    card.classList.remove('selected');
                });
                
                // Sélectionner cette table
                this.classList.add('selected');
                selectedTableId = table.id;
            });
            
            editTablesList.appendChild(tableCard);
        });
    }
    
    // Soumettre le formulaire de modification
    editForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!checkUserLoggedIn()) return;
        
        const reservationId = document.getElementById('edit-reservation-id').value;
        const newDate = document.getElementById('edit-date').value;
        const newTime = document.getElementById('edit-time').value;
        
        if (!reservationId || !newDate || !newTime || !selectedTableId) {
            showMessage(editMessage, 'Veuillez remplir tous les champs et sélectionner une table.', 'error');
            return;
        }
        
        try {
            // Appel au contrôleur pour mettre à jour la réservation
            const result = await ReservationController.updateReservation(reservationId, selectedTableId, newDate, newTime);
            
            if (result.success) {
                showMessage(editMessage, result.message || 'Réservation mise à jour avec succès!', 'success');
                
                // Fermer la modal après un délai et rafraîchir les réservations
                setTimeout(() => {
                    closeEditModal();
                    showManageReservations();
                }, 2000);
            } else {
                showMessage(editMessage, result.message || 'Erreur lors de la mise à jour.', 'error');
            }
        } catch (error) {
            console.error('Erreur:', error);
            showMessage(editMessage, 'Une erreur est survenue lors de la mise à jour.', 'error');
        }
    });
    
    // Fermer la modal
    function closeEditModal() {
        editModal.classList.remove('show');
        editMessage.textContent = '';
        editMessage.className = 'message';
        selectedTableId = null;
    }
    
    // Bouton de fermeture de la modal
    document.querySelector('.close').addEventListener('click', closeEditModal);
    
    // Fermer la modal en cliquant en dehors
    window.addEventListener('click', function(e) {
        if (e.target === editModal) {
            closeEditModal();
        }
    });
    
    // Annuler une réservation
    async function cancelReservation(reservationId) {
        if (!checkUserLoggedIn()) return;
        
        if (!confirm('Êtes-vous sûr de vouloir annuler cette réservation?')) {
            return;
        }
        
        try {
            // Appel au contrôleur pour annuler la réservation
            const result = await ReservationController.cancelReservation(reservationId);
            
            if (result.success) {
                alert(result.message || 'Réservation annulée avec succès!');
                showManageReservations(); // Rafraîchir la liste
            } else {
                alert(result.message || 'Erreur lors de l\'annulation.');
            }
        } catch (error) {
            console.error('Erreur:', error);
            alert('Une erreur est survenue lors de l\'annulation.');
        }
    }
    
    // Boutons de navigation
    backToAvailabilityBtn.addEventListener('click', function() {
        tablesSection.classList.add('hidden');
        availabilitySection.classList.remove('hidden');
    });
    
    backToTablesBtn.addEventListener('click', function() {
        confirmationSection.classList.add('hidden');
        tablesSection.classList.remove('hidden');
    });
    
    viewReservationsBtn.addEventListener('click', function() {
        showManageReservations();
    });
    
    backToMainBtn.addEventListener('click', function() {
        manageReservationsSection.classList.add('hidden');
        availabilitySection.classList.remove('hidden');
        // Réinitialiser les valeurs
        selectedTableId = null;
        confirmationMessage.textContent = '';
        confirmationMessage.className = 'message';
    });
    
    // Initialiser la page
    init();
});