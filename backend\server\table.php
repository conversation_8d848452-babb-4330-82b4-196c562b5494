<?php
require_once 'database.php';

// Classe pour gérer les opérations liées aux tables
class TableAPI {
    private $conn;
    
    public function __construct() {
        $this->conn = connectDB();
    }
    
    // Obtenir toutes les tables
    public function getAllTables() {
        try {
            $stmt = $this->conn->prepare("SELECT id, seats, status FROM `Table` ORDER BY seats ASC");
            $stmt->execute();
            
            $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ["success" => true, "tables" => $tables];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des tables: " . $e->getMessage()];
        }
    }
    
    // Obtenir une table par son ID
    public function getTableById($tableId) {
        try {
            $stmt = $this->conn->prepare("SELECT id, seats, status FROM `Table` WHERE id = ?");
            $stmt->execute([$tableId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Table non trouvée."];
            }
            
            $table = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return ["success" => true, "table" => $table];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération de la table: " . $e->getMessage()];
        }
    }
    
    // Ajouter une nouvelle table (pour l'admin/manager)
    public function addTable($seats, $status = 'Available') {
        try {
            $stmt = $this->conn->prepare("INSERT INTO `Table` (seats, status) VALUES (?, ?)");
            $stmt->execute([$seats, $status]);
            
            $tableId = $this->conn->lastInsertId();
            
            return ["success" => true, "message" => "Table ajoutée avec succès!", "table_id" => $tableId];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de l'ajout de la table: " . $e->getMessage()];
        }
    }
    
    // Mettre à jour une table (pour l'admin/manager)
    public function updateTable($tableId, $seats, $status) {
        try {
            $stmt = $this->conn->prepare("UPDATE `Table` SET seats = ?, status = ? WHERE id = ?");
            $stmt->execute([$seats, $status, $tableId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Table non trouvée ou aucune modification effectuée."];
            }
            
            return ["success" => true, "message" => "Table mise à jour avec succès!"];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la mise à jour de la table: " . $e->getMessage()];
        }
    }
    
    // Supprimer une table (pour l'admin/manager)
    public function deleteTable($tableId) {
        try {
            // Vérifier s'il y a des réservations actives pour cette table
            $stmt = $this->conn->prepare("SELECT COUNT(*) FROM Reservation WHERE table_id = ? AND status != 'Cancelled' AND date_time > NOW()");
            $stmt->execute([$tableId]);
            $activeReservations = $stmt->fetchColumn();
            
            if ($activeReservations > 0) {
                return ["success" => false, "message" => "Impossible de supprimer la table: il y a des réservations actives."];
            }
            
            $stmt = $this->conn->prepare("DELETE FROM `Table` WHERE id = ?");
            $stmt->execute([$tableId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Table non trouvée."];
            }
            
            return ["success" => true, "message" => "Table supprimée avec succès!"];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la suppression de la table: " . $e->getMessage()];
        }
    }
    
    // Obtenir les tables disponibles pour une date/heure donnée
    public function getAvailableTables($date, $time, $minSeats = 1) {
        try {
            // Combiner la date et l'heure
            $dateTime = $date . ' ' . $time . ':00';
            
            // Trouver les tables qui ont le nombre de places requis ou plus
            $stmt = $this->conn->prepare(
                "SELECT id, seats, status FROM `Table` 
                WHERE seats >= ? AND status = 'Available' AND id NOT IN (
                    SELECT table_id FROM Reservation 
                    WHERE date_time BETWEEN DATE_SUB(?, INTERVAL 2 HOUR) AND DATE_ADD(?, INTERVAL 2 HOUR)
                    AND status != 'Cancelled'
                )
                ORDER BY seats ASC"
            );
            $stmt->execute([$minSeats, $dateTime, $dateTime]);
            
            $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ["success" => true, "tables" => $tables];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la vérification de disponibilité: " . $e->getMessage()];
        }
    }
}

// Traitement des requêtes API
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $tableAPI = new TableAPI();
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'getAllTables':
            $result = $tableAPI->getAllTables();
            echo json_encode($result);
            break;
            
        case 'getTableById':
            if (isset($_GET['id'])) {
                $result = $tableAPI->getTableById($_GET['id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de table manquant."]);
            }
            break;
            
        case 'getAvailableTables':
            if (isset($_GET['date'], $_GET['time'])) {
                $minSeats = $_GET['minSeats'] ?? 1;
                $result = $tableAPI->getAvailableTables($_GET['date'], $_GET['time'], $minSeats);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Date et heure manquantes."]);
            }
            break;
            
        default:
            echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tableAPI = new TableAPI();
    $data = json_decode(file_get_contents('php://input'), true);
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'addTable':
            if (isset($data['seats'])) {
                $status = $data['status'] ?? 'Available';
                $result = $tableAPI->addTable($data['seats'], $status);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Nombre de places manquant."]);
            }
            break;
            
        case 'updateTable':
            if (isset($data['table_id'], $data['seats'], $data['status'])) {
                $result = $tableAPI->updateTable($data['table_id'], $data['seats'], $data['status']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour la mise à jour."]);
            }
            break;
            
        case 'deleteTable':
            if (isset($data['table_id'])) {
                $result = $tableAPI->deleteTable($data['table_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de table manquant."]);
            }
            break;
            
        default:
            echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
}
?>
