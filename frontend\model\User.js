/**
 * Classe modèle pour les utilisateurs
 */
class User {
    constructor(id, email, role, status) {
        this.id = id;
        this.email = email;
        this.role = role;
        this.status = status;
    }

    // Méthode pour l'inscription d'un nouvel utilisateur
    static async register(name, email, phone, password) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, email, phone, password })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de l\'inscription:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour la connexion d'un utilisateur
    static async login(email, password) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la connexion:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour la déconnexion
    static async logout() {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=logout');
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour obtenir les informations de l'utilisateur
    static async getUserInfo(userId) {
        try {
            const response = await fetch(`http://restaurantmila.atwebpages.com/sever/user.php?action=getUserInfo&userId=${userId}`);
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération des informations utilisateur:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour mettre à jour les informations du client
    static async updateClientInfo(clientId, name, phone) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=updateClientInfo', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId, name, phone })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la mise à jour des informations:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour changer le mot de passe
    static async changePassword(userId, currentPassword, newPassword) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=changePassword', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ userId, currentPassword, newPassword })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors du changement de mot de passe:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }
}