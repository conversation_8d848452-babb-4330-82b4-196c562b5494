<?php
require_once 'database.php';

// Classe pour gérer les opérations liées aux utilisateurs
class UserAPI {
    private $conn;

    public function __construct() {
        $this->conn = connectDB();
    }

    // Inscription d'un nouvel utilisateur
    public function register($email, $password, $name, $phone) {
        try {
            // Vérifier si l'email existe déjà
            $stmt = $this->conn->prepare("SELECT id FROM User WHERE email = ?");
            $stmt->execute([$email]);

            if ($stmt->rowCount() > 0) {
                return ["success" => false, "message" => "Cet email est déjà utilisé."];
            }

            // Hachage du mot de passe
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Début de la transaction
            $this->conn->beginTransaction();

            // Insertion dans la table User
            $stmt = $this->conn->prepare("INSERT INTO User (email, password, role, status) VALUES (?, ?, 'Client', TRUE)");
            $stmt->execute([$email, $hashedPassword]);

            $userId = $this->conn->lastInsertId();

            // Insertion dans la table Client
            $stmt = $this->conn->prepare("INSERT INTO Client (name, phone, user_id) VALUES (?, ?, ?)");
            $stmt->execute([$name, $phone, $userId]);

            // Validation de la transaction
            $this->conn->commit();

            return ["success" => true, "message" => "Inscription réussie!"];

        } catch (PDOException $e) {
            // Annulation de la transaction en cas d'erreur
            $this->conn->rollBack();
            return ["success" => false, "message" => "Erreur lors de l'inscription: " . $e->getMessage()];
        }
    }

    // Connexion d'un utilisateur
    public function login($email, $password) {
        try {
            $stmt = $this->conn->prepare("SELECT User.id, User.password, User.role, User.status, Client.id as client_id, Client.name
                                         FROM User
                                         LEFT JOIN Client ON User.id = Client.user_id
                                         WHERE User.email = ?");
            $stmt->execute([$email]);

            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Email ou mot de passe incorrect."];
            }

            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user['status']) {
                return ["success" => false, "message" => "Votre compte est désactivé."];
            }

            if (!password_verify($password, $user['password'])) {
                return ["success" => false, "message" => "Email ou mot de passe incorrect."];
            }

            // Création de la session
            session_start();
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['role'] = $user['role'];

            if ($user['role'] == 'Client') {
                $_SESSION['client_id'] = $user['client_id'];
                $_SESSION['name'] = $user['name'];
            }

            return [
                "success" => true,
                "message" => "Connexion réussie!",
                "user" => [
                    "id" => $user['id'],
                    "role" => $user['role'],
                    "client_id" => $user['client_id'] ?? null,
                    "name" => $user['name'] ?? null
                ]
            ];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la connexion: " . $e->getMessage()];
        }
    }

    // Déconnexion
    public function logout() {
        session_start();
        session_destroy();
        return ["success" => true, "message" => "Déconnexion réussie!"];
    }

    // Récupérer les informations d'un utilisateur
    public function getUserInfo($userId) {
        try {
            $stmt = $this->conn->prepare("SELECT User.id, User.email, User.role, User.status, Client.id as client_id, Client.name, Client.phone
                                         FROM User
                                         LEFT JOIN Client ON User.id = Client.user_id
                                         WHERE User.id = ?");
            $stmt->execute([$userId]);

            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Utilisateur non trouvé."];
            }

            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            return [
                "success" => true,
                "user" => [
                    "id" => $user['id'],
                    "email" => $user['email'],
                    "role" => $user['role'],
                    "status" => $user['status'],
                    "client_id" => $user['client_id'] ?? null,
                    "name" => $user['name'] ?? null,
                    "phone" => $user['phone'] ?? null
                ]
            ];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des informations: " . $e->getMessage()];
        }
    }

    // Mettre à jour les informations d'un client
    public function updateClientInfo($clientId, $name, $phone) {
        try {
            $stmt = $this->conn->prepare("UPDATE Client SET name = ?, phone = ? WHERE id = ?");
            $stmt->execute([$name, $phone, $clientId]);

            return ["success" => true, "message" => "Informations mises à jour avec succès!"];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la mise à jour: " . $e->getMessage()];
        }
    }

    // Changer le mot de passe
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            $stmt = $this->conn->prepare("SELECT password FROM User WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!password_verify($currentPassword, $user['password'])) {
                return ["success" => false, "message" => "Mot de passe actuel incorrect."];
            }

            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            $stmt = $this->conn->prepare("UPDATE User SET password = ? WHERE id = ?");
            $stmt->execute([$hashedPassword, $userId]);

            return ["success" => true, "message" => "Mot de passe changé avec succès!"];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors du changement de mot de passe: " . $e->getMessage()];
        }
    }

    // Fonctions d'administration

    // Récupérer tous les utilisateurs (pour l'admin)
    public function getAllUsers() {
        try {
            $stmt = $this->conn->prepare("SELECT User.id, User.email, User.role, User.status, Client.name
                                         FROM User
                                         LEFT JOIN Client ON User.id = Client.user_id");
            $stmt->execute();

            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ["success" => true, "users" => $users];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des utilisateurs: " . $e->getMessage()];
        }
    }

    // Changer le statut d'un utilisateur (activer/désactiver)
    public function changeUserStatus($userId, $status) {
        try {
            $stmt = $this->conn->prepare("UPDATE User SET status = ? WHERE id = ?");
            $stmt->execute([$status, $userId]);

            return ["success" => true, "message" => "Statut de l'utilisateur mis à jour avec succès!"];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la mise à jour du statut: " . $e->getMessage()];
        }
    }

    // Changer le rôle d'un utilisateur
    public function changeUserRole($userId, $role) {
        try {
            $stmt = $this->conn->prepare("UPDATE User SET role = ? WHERE id = ?");
            $stmt->execute([$role, $userId]);

            return ["success" => true, "message" => "Rôle de l'utilisateur mis à jour avec succès!"];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la mise à jour du rôle: " . $e->getMessage()];
        }
    }
}

// Traitement des requêtes GET
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $userAPI = new UserAPI();
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'getUserInfo':
            if (isset($_GET['userId'])) {
                $result = $userAPI->getUserInfo($_GET['userId']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID utilisateur manquant."]);
            }
            break;

        default:
            echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
}

// Traitement des requêtes API
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $userAPI = new UserAPI();
    $data = json_decode(file_get_contents('php://input'), true);
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'register':
            if (isset($data['email'], $data['password'], $data['name'], $data['phone'])) {
                $result = $userAPI->register($data['email'], $data['password'], $data['name'], $data['phone']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour l'inscription."]);
            }
            break;

        case 'login':
            if (isset($data['email'], $data['password'])) {
                $result = $userAPI->login($data['email'], $data['password']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour la connexion."]);
            }
            break;

        case 'logout':
            $result = $userAPI->logout();
            echo json_encode($result);
            break;

        case 'update_client':
            if (isset($data['client_id'], $data['name'], $data['phone'])) {
                $result = $userAPI->updateClientInfo($data['client_id'], $data['name'], $data['phone']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour la mise à jour."]);
            }
            break;

        case 'change_password':
            if (isset($data['user_id'], $data['current_password'], $data['new_password'])) {
                $result = $userAPI->changePassword($data['user_id'], $data['current_password'], $data['new_password']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour le changement de mot de passe."]);
            }
            break;

        case 'get_all_users':
            $result = $userAPI->getAllUsers();
            echo json_encode($result);
            break;

        case 'change_user_status':
            if (isset($data['user_id'], $data['status'])) {
                $result = $userAPI->changeUserStatus($data['user_id'], $data['status']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour la mise à jour du statut."]);
            }
            break;

        case 'change_user_role':
            if (isset($data['user_id'], $data['role'])) {
                $result = $userAPI->changeUserRole($data['user_id'], $data['role']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour la mise à jour du rôle."]);
            }
            break;

        default:
            echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
}
?>