/* Styles généraux */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: #3498db;
}

/* Header */
header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.logo h1 {
    color: #2c3e50;
    font-size: 24px;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    color: #2c3e50;
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s;
}

nav ul li a:hover, nav ul li a.active {
    color: #3498db;
}

.btn-primary {
    background-color: #3498db;
    color: white !important;
    padding: 10px 15px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    border: 1px solid #3498db;
    color: #3498db !important;
    padding: 10px 15px;
    border-radius: 4px;
    transition: all 0.3s;
}

.btn-secondary:hover {
    background-color: #3498db;
    color: white !important;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://via.placeholder.com/1920x1080') no-repeat center center/cover;
    height: 100vh;
    display: flex;
    align-items: center;
    text-align: center;
    color: white;
    margin-bottom: 60px;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero h2 {
    font-size: 48px;
    margin-bottom: 20px;
}

.hero p {
    font-size: 20px;
    margin-bottom: 30px;
}

/* About Section */
.about {
    padding: 80px 0;
    background-color: white;
}

.about h2 {
    text-align: center;
    margin-bottom: 40px;
    color: #2c3e50;
    font-size: 36px;
}

.about-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 40px;
}

.about-text {
    flex: 1;
    min-width: 300px;
}

.about-text p {
    margin-bottom: 20px;
    font-size: 16px;
}

.about-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
}

.about-image img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Menu Preview Section */
.menu-preview {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.menu-preview h2 {
    text-align: center;
    margin-bottom: 40px;
    color: #2c3e50;
    font-size: 36px;
}

.menu-categories {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.menu-category {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    flex: 1;
    min-width: 300px;
    max-width: 350px;
    text-align: center;
    transition: transform 0.3s;
}

.menu-category:hover {
    transform: translateY(-10px);
}

.menu-category h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 24px;
}

.menu-category p {
    margin-bottom: 20px;
    color: #666;
}

/* Reservation CTA Section */
.reservation-cta {
    padding: 80px 0;
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://via.placeholder.com/1920x1080') no-repeat center center/cover;
    color: white;
    text-align: center;
}

.reservation-cta h2 {
    font-size: 36px;
    margin-bottom: 20px;
}

.reservation-cta p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40px;
}

.footer-info, .footer-hours, .footer-links {
    flex: 1;
    min-width: 250px;
    margin-bottom: 30px;
}

footer h3 {
    margin-bottom: 20px;
    font-size: 20px;
}

footer p {
    margin-bottom: 10px;
    color: #ecf0f1;
}

.footer-links ul {
    list-style: none;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #ecf0f1;
    transition: color 0.3s;
}

.footer-links ul li a:hover {
    color: #3498db;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }
    
    nav ul {
        margin-top: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    nav ul li {
        margin: 5px 10px;
    }
    
    .hero h2 {
        font-size: 36px;
    }
    
    .hero p {
        font-size: 18px;
    }
    
    .about-content, .menu-categories, .footer-content {
        flex-direction: column;
    }
    
    .about-image {
        order: -1;
    }
}

@media (max-width: 480px) {
    .hero h2 {
        font-size: 28px;
    }
    
    .hero p {
        font-size: 16px;
    }
    
    .about h2, .menu-preview h2, .reservation-cta h2 {
        font-size: 28px;
    }
    
    .menu-category {
        min-width: 100%;
    }
}