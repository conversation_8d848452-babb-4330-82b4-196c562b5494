<?php
require_once 'database.php';

// Classe pour gérer les opérations liées aux menus et plats
class MenuAPI {
    private $conn;
    
    public function __construct() {
        $this->conn = connectDB();
    }
    
    // Obtenir tous les menus avec leurs plats
    public function getAllMenus() {
        try {
            $stmt = $this->conn->prepare("SELECT id, name FROM Menu ORDER BY name ASC");
            $stmt->execute();
            
            $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Pour chaque menu, récupérer ses plats
            foreach ($menus as &$menu) {
                $dishStmt = $this->conn->prepare("SELECT id, name, description, price FROM Dish WHERE menu_id = ? ORDER BY name ASC");
                $dishStmt->execute([$menu['id']]);
                $menu['dishes'] = $dishStmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            return ["success" => true, "menus" => $menus];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des menus: " . $e->getMessage()];
        }
    }
    
    // Obtenir un menu par son ID
    public function getMenuById($menuId) {
        try {
            $stmt = $this->conn->prepare("SELECT id, name FROM Menu WHERE id = ?");
            $stmt->execute([$menuId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Menu non trouvé."];
            }
            
            $menu = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Récupérer les plats du menu
            $dishStmt = $this->conn->prepare("SELECT id, name, description, price FROM Dish WHERE menu_id = ? ORDER BY name ASC");
            $dishStmt->execute([$menuId]);
            $menu['dishes'] = $dishStmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ["success" => true, "menu" => $menu];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération du menu: " . $e->getMessage()];
        }
    }
    
    // Ajouter un nouveau menu
    public function addMenu($name) {
        try {
            $stmt = $this->conn->prepare("INSERT INTO Menu (name) VALUES (?)");
            $stmt->execute([$name]);
            
            $menuId = $this->conn->lastInsertId();
            
            return ["success" => true, "message" => "Menu ajouté avec succès!", "menu_id" => $menuId];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de l'ajout du menu: " . $e->getMessage()];
        }
    }
    
    // Mettre à jour un menu
    public function updateMenu($menuId, $name) {
        try {
            $stmt = $this->conn->prepare("UPDATE Menu SET name = ? WHERE id = ?");
            $stmt->execute([$name, $menuId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Menu non trouvé ou aucune modification effectuée."];
            }
            
            return ["success" => true, "message" => "Menu mis à jour avec succès!"];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la mise à jour du menu: " . $e->getMessage()];
        }
    }
    
    // Supprimer un menu
    public function deleteMenu($menuId) {
        try {
            // Vérifier s'il y a des plats dans ce menu
            $stmt = $this->conn->prepare("SELECT COUNT(*) FROM Dish WHERE menu_id = ?");
            $stmt->execute([$menuId]);
            $dishCount = $stmt->fetchColumn();
            
            if ($dishCount > 0) {
                return ["success" => false, "message" => "Impossible de supprimer le menu: il contient des plats."];
            }
            
            $stmt = $this->conn->prepare("DELETE FROM Menu WHERE id = ?");
            $stmt->execute([$menuId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Menu non trouvé."];
            }
            
            return ["success" => true, "message" => "Menu supprimé avec succès!"];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la suppression du menu: " . $e->getMessage()];
        }
    }
    
    // Ajouter un nouveau plat
    public function addDish($name, $description, $price, $menuId) {
        try {
            $stmt = $this->conn->prepare("INSERT INTO Dish (name, description, price, menu_id) VALUES (?, ?, ?, ?)");
            $stmt->execute([$name, $description, $price, $menuId]);
            
            $dishId = $this->conn->lastInsertId();
            
            return ["success" => true, "message" => "Plat ajouté avec succès!", "dish_id" => $dishId];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de l'ajout du plat: " . $e->getMessage()];
        }
    }
    
    // Mettre à jour un plat
    public function updateDish($dishId, $name, $description, $price, $menuId) {
        try {
            $stmt = $this->conn->prepare("UPDATE Dish SET name = ?, description = ?, price = ?, menu_id = ? WHERE id = ?");
            $stmt->execute([$name, $description, $price, $menuId, $dishId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Plat non trouvé ou aucune modification effectuée."];
            }
            
            return ["success" => true, "message" => "Plat mis à jour avec succès!"];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la mise à jour du plat: " . $e->getMessage()];
        }
    }
    
    // Supprimer un plat
    public function deleteDish($dishId) {
        try {
            $stmt = $this->conn->prepare("DELETE FROM Dish WHERE id = ?");
            $stmt->execute([$dishId]);
            
            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Plat non trouvé."];
            }
            
            return ["success" => true, "message" => "Plat supprimé avec succès!"];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la suppression du plat: " . $e->getMessage()];
        }
    }
    
    // Obtenir tous les plats
    public function getAllDishes() {
        try {
            $stmt = $this->conn->prepare(
                "SELECT d.id, d.name, d.description, d.price, d.menu_id, m.name as menu_name 
                FROM Dish d 
                JOIN Menu m ON d.menu_id = m.id 
                ORDER BY m.name, d.name"
            );
            $stmt->execute();
            
            $dishes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return ["success" => true, "dishes" => $dishes];
            
        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des plats: " . $e->getMessage()];
        }
    }
}

// Traitement des requêtes GET
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $menuAPI = new MenuAPI();
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'getAllMenus':
            $result = $menuAPI->getAllMenus();
            echo json_encode($result);
            break;
            
        case 'getMenuById':
            if (isset($_GET['id'])) {
                $result = $menuAPI->getMenuById($_GET['id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de menu manquant."]);
            }
            break;
            
        case 'getAllDishes':
            $result = $menuAPI->getAllDishes();
            echo json_encode($result);
            break;
            
        default:
            echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
}

// Traitement des requêtes POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $menuAPI = new MenuAPI();
    $data = json_decode(file_get_contents('php://input'), true);
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'addMenu':
            if (isset($data['name'])) {
                $result = $menuAPI->addMenu($data['name']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Nom du menu manquant."]);
            }
            break;
            
        case 'updateMenu':
            if (isset($data['menu_id'], $data['name'])) {
                $result = $menuAPI->updateMenu($data['menu_id'], $data['name']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour la mise à jour du menu."]);
            }
            break;
            
        case 'deleteMenu':
            if (isset($data['menu_id'])) {
                $result = $menuAPI->deleteMenu($data['menu_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de menu manquant."]);
            }
            break;
            
        case 'addDish':
            if (isset($data['name'], $data['description'], $data['price'], $data['menu_id'])) {
                $result = $menuAPI->addDish($data['name'], $data['description'], $data['price'], $data['menu_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour l'ajout du plat."]);
            }
            break;
            
        case 'updateDish':
            if (isset($data['dish_id'], $data['name'], $data['description'], $data['price'], $data['menu_id'])) {
                $result = $menuAPI->updateDish($data['dish_id'], $data['name'], $data['description'], $data['price'], $data['menu_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour la mise à jour du plat."]);
            }
            break;
            
        case 'deleteDish':
            if (isset($data['dish_id'])) {
                $result = $menuAPI->deleteDish($data['dish_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de plat manquant."]);
            }
            break;
            
        default:
            echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
}
?>
