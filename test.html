<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test du Système de Réservation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #c0392b;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Test du Système de Réservation de Restaurant</h1>

    <div class="test-section">
        <h2>Tests de Base de Données</h2>
        <button onclick="testDatabaseConnection()">Tester la Connexion DB</button>
        <button onclick="testTableStructure()">Vérifier les Tables</button>
        <div id="db-results"></div>
    </div>

    <div class="test-section">
        <h2>Tests des API Utilisateurs</h2>
        <button onclick="testUserRegistration()">Test Inscription</button>
        <button onclick="testUserLogin()">Test Connexion</button>
        <button onclick="testGetAllUsers()">Lister Utilisateurs</button>
        <div id="user-results"></div>
    </div>

    <div class="test-section">
        <h2>Tests des API Réservations</h2>
        <button onclick="testCheckAvailability()">Test Disponibilité</button>
        <button onclick="testCreateReservation()">Test Création Réservation</button>
        <button onclick="testGetReservations()">Lister Réservations</button>
        <div id="reservation-results"></div>
    </div>

    <div class="test-section">
        <h2>Tests des API Tables</h2>
        <button onclick="testGetAllTables()">Lister Tables</button>
        <button onclick="testAddTable()">Ajouter Table</button>
        <div id="table-results"></div>
    </div>

    <div class="test-section">
        <h2>Tests d'Interface</h2>
        <button onclick="testFrontendPages()">Tester Pages Frontend</button>
        <button onclick="testNavigation()">Tester Navigation</button>
        <div id="frontend-results"></div>
    </div>

    <div class="test-section">
        <h2>Résumé des Tests</h2>
        <button onclick="runAllTests()" style="background-color: #28a745;">Exécuter Tous les Tests</button>
        <button onclick="clearResults()" class="btn-secondary">Effacer Résultats</button>
        <div id="summary-results"></div>
    </div>

    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        function addResult(containerId, message, type, details = null) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <strong>${message}</strong>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            container.appendChild(resultDiv);

            testResults.total++;
            if (type === 'success') testResults.passed++;
            else if (type === 'error') testResults.failed++;

            updateSummary();
        }

        function updateSummary() {
            const summaryContainer = document.getElementById('summary-results');
            summaryContainer.innerHTML = `
                <div class="test-result info">
                    <strong>Résumé:</strong> ${testResults.passed} réussis, ${testResults.failed} échoués, ${testResults.total} total
                </div>
            `;
        }

        function clearResults() {
            const containers = ['db-results', 'user-results', 'reservation-results', 'table-results', 'frontend-results', 'summary-results'];
            containers.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            testResults = { passed: 0, failed: 0, total: 0 };
        }

        // Tests de base de données
        async function testDatabaseConnection() {
            try {
                const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=get_all_users', {
                    method: 'POST'
                });
                const data = await response.json();

                if (response.ok && data) {
                    addResult('db-results', 'Connexion à la base de données: SUCCÈS', 'success');
                } else {
                    addResult('db-results', 'Connexion à la base de données: ÉCHEC', 'error', data);
                }
            } catch (error) {
                addResult('db-results', 'Connexion à la base de données: ERREUR', 'error', error.message);
            }
        }

        async function testTableStructure() {
            try {
                const response = await fetch('backend/server/table.php?action=getAllTables');
                const data = await response.json();

                if (data.success) {
                    addResult('db-results', 'Structure des tables: SUCCÈS', 'success', `${data.tables.length} tables trouvées`);
                } else {
                    addResult('db-results', 'Structure des tables: ÉCHEC', 'error', data);
                }
            } catch (error) {
                addResult('db-results', 'Structure des tables: ERREUR', 'error', error.message);
            }
        }

        // Tests des utilisateurs
        async function testUserRegistration() {
            const testUser = {
                name: 'Test User',
                email: '<EMAIL>',
                phone: '0123456789',
                password: 'testpassword123'
            };

            try {
                const response = await fetch('backend/server/user.php?action=register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testUser)
                });
                const data = await response.json();

                if (data.success) {
                    addResult('user-results', 'Inscription utilisateur: SUCCÈS', 'success', data.message);
                } else {
                    addResult('user-results', 'Inscription utilisateur: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('user-results', 'Inscription utilisateur: ERREUR', 'error', error.message);
            }
        }

        async function testUserLogin() {
            const loginData = {
                email: '<EMAIL>',
                password: 'testpassword123'
            };

            try {
                const response = await fetch('backend/server/user.php?action=login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData)
                });
                const data = await response.json();

                if (data.success) {
                    addResult('user-results', 'Connexion utilisateur: SUCCÈS', 'success', data.message);
                    // Stocker les données utilisateur pour les tests suivants
                    window.testUser = data.user;
                } else {
                    addResult('user-results', 'Connexion utilisateur: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('user-results', 'Connexion utilisateur: ERREUR', 'error', error.message);
            }
        }

        async function testGetAllUsers() {
            try {
                const response = await fetch('backend/server/user.php?action=get_all_users', {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    addResult('user-results', 'Liste des utilisateurs: SUCCÈS', 'success', `${data.users.length} utilisateurs trouvés`);
                } else {
                    addResult('user-results', 'Liste des utilisateurs: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('user-results', 'Liste des utilisateurs: ERREUR', 'error', error.message);
            }
        }

        // Tests des réservations
        async function testCheckAvailability() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const dateStr = tomorrow.toISOString().split('T')[0];

            const availabilityData = {
                date: dateStr,
                time: '19:00',
                seats: 2
            };

            try {
                const response = await fetch('backend/server/reservation.php?action=check_availability', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(availabilityData)
                });
                const data = await response.json();

                if (data.success) {
                    addResult('reservation-results', 'Vérification disponibilité: SUCCÈS', 'success', `${data.tables.length} tables disponibles`);
                } else {
                    addResult('reservation-results', 'Vérification disponibilité: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('reservation-results', 'Vérification disponibilité: ERREUR', 'error', error.message);
            }
        }

        async function testCreateReservation() {
            if (!window.testUser || !window.testUser.client_id) {
                addResult('reservation-results', 'Création réservation: ÉCHEC', 'error', 'Utilisateur de test non connecté');
                return;
            }

            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const dateStr = tomorrow.toISOString().split('T')[0];

            const reservationData = {
                client_id: window.testUser.client_id,
                table_id: 1,
                date: dateStr,
                time: '19:00'
            };

            try {
                const response = await fetch('backend/server/reservation.php?action=create_reservation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(reservationData)
                });
                const data = await response.json();

                if (data.success) {
                    addResult('reservation-results', 'Création réservation: SUCCÈS', 'success', data.message);
                    window.testReservationId = data.reservation_id;
                } else {
                    addResult('reservation-results', 'Création réservation: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('reservation-results', 'Création réservation: ERREUR', 'error', error.message);
            }
        }

        async function testGetReservations() {
            try {
                const response = await fetch('backend/server/reservation.php?action=get_all_reservations', {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    addResult('reservation-results', 'Liste des réservations: SUCCÈS', 'success', `${data.reservations.length} réservations trouvées`);
                } else {
                    addResult('reservation-results', 'Liste des réservations: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('reservation-results', 'Liste des réservations: ERREUR', 'error', error.message);
            }
        }

        // Tests des tables
        async function testGetAllTables() {
            try {
                const response = await fetch('backend/server/table.php?action=getAllTables');
                const data = await response.json();

                if (data.success) {
                    addResult('table-results', 'Liste des tables: SUCCÈS', 'success', `${data.tables.length} tables trouvées`);
                } else {
                    addResult('table-results', 'Liste des tables: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('table-results', 'Liste des tables: ERREUR', 'error', error.message);
            }
        }

        async function testAddTable() {
            const tableData = {
                seats: 4,
                status: 'Available'
            };

            try {
                const response = await fetch('backend/server/table.php?action=addTable', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(tableData)
                });
                const data = await response.json();

                if (data.success) {
                    addResult('table-results', 'Ajout de table: SUCCÈS', 'success', data.message);
                } else {
                    addResult('table-results', 'Ajout de table: ÉCHEC', 'error', data.message);
                }
            } catch (error) {
                addResult('table-results', 'Ajout de table: ERREUR', 'error', error.message);
            }
        }

        // Tests du frontend
        function testFrontendPages() {
            const pages = [
                'frontend/index.html',
                'frontend/view/login/login.html',
                'frontend/view/signup/signup.html',
                'frontend/view/reservation/reservation.html',
                'frontend/view/admin/dashboard.html',
                'frontend/view/manager/dashboard.html'
            ];

            pages.forEach(async (page) => {
                try {
                    const response = await fetch(page);
                    if (response.ok) {
                        addResult('frontend-results', `Page ${page}: SUCCÈS`, 'success');
                    } else {
                        addResult('frontend-results', `Page ${page}: ÉCHEC`, 'error', `Status: ${response.status}`);
                    }
                } catch (error) {
                    addResult('frontend-results', `Page ${page}: ERREUR`, 'error', error.message);
                }
            });
        }

        function testNavigation() {
            // Test de base de la navigation JavaScript
            try {
                if (typeof UserController !== 'undefined') {
                    addResult('frontend-results', 'UserController: SUCCÈS', 'success');
                } else {
                    addResult('frontend-results', 'UserController: ÉCHEC', 'error', 'Classe non définie');
                }
            } catch (error) {
                addResult('frontend-results', 'Navigation: ERREUR', 'error', error.message);
            }
        }

        // Exécuter tous les tests
        async function runAllTests() {
            clearResults();
            addResult('summary-results', 'Début des tests...', 'info');

            // Tests séquentiels pour éviter les conflits
            await testDatabaseConnection();
            await testTableStructure();
            await testGetAllTables();
            await testAddTable();
            await testGetAllUsers();
            await testUserRegistration();
            await testUserLogin();
            await testCheckAvailability();
            await testCreateReservation();
            await testGetReservations();
            testFrontendPages();
            testNavigation();

            setTimeout(() => {
                addResult('summary-results', 'Tests terminés!', 'info');
            }, 2000);
        }
    </script>
</body>
</html>
