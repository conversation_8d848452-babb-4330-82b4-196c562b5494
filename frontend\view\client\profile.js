/**
 * Script pour le profil client
 */

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier l'authentification
    checkClientAuth();

    // Charger les données du profil
    loadProfileData();

    // Charger les réservations
    loadReservations();

    // Charger les statistiques
    loadStatistics();

    // Initialiser les gestionnaires d'événements
    initializeEventHandlers();
});

let currentUser = null;
let allReservations = [];

/**
 * Vérifie l'authentification client
 */
function checkClientAuth() {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user || user.role !== 'Client') {
        alert('Accès non autorisé. Redirection vers la page de connexion.');
        window.location.href = '../login/login.html';
        return;
    }

    currentUser = user;
    console.log('Utilisateur client connecté:', user);
}

/**
 * Charge les données du profil
 */
async function loadProfileData() {
    try {
        const response = await fetch(`http://restaurantmila.atwebpages.com/sever/user.php?action=getUserInfo&userId=${currentUser.id}`);
        const data = await response.json();

        if (data.success) {
            const user = data.user;
            document.getElementById('name').value = user.name || '';
            document.getElementById('email').value = user.email || '';
            document.getElementById('phone').value = user.phone || '';
        } else {
            showMessage('profile-message', 'Erreur lors du chargement du profil: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('profile-message', 'Erreur lors du chargement du profil.', 'error');
    }
}

/**
 * Charge les réservations du client
 */
async function loadReservations() {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=get_client_reservations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ client_id: currentUser.client_id })
        });
        const data = await response.json();

        if (data.success) {
            allReservations = data.reservations;
            displayReservations(allReservations);
        } else {
            showMessage('profile-message', 'Erreur lors du chargement des réservations: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('profile-message', 'Erreur lors du chargement des réservations.', 'error');
    }
}

/**
 * Affiche les réservations
 */
function displayReservations(reservations) {
    const container = document.getElementById('reservations-list');

    if (reservations.length === 0) {
        container.innerHTML = '<p class="text-center">Aucune réservation trouvée.</p>';
        return;
    }

    container.innerHTML = reservations.map(reservation => {
        const date = new Date(reservation.date_time);
        const formattedDate = date.toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        const formattedTime = date.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const isPast = date < new Date();
        const canModify = !isPast && reservation.status !== 'Cancelled';

        return `
            <div class="reservation-item">
                <div class="reservation-header">
                    <div class="reservation-date">${formattedDate} à ${formattedTime}</div>
                    <span class="reservation-status status-${reservation.status.toLowerCase()}">
                        ${getStatusText(reservation.status)}
                    </span>
                </div>
                <div class="reservation-details">
                    <p><strong>Table:</strong> ${reservation.table_id} (${reservation.seats} places)</p>
                    <p><strong>Statut:</strong> ${getStatusText(reservation.status)}</p>
                </div>
                <div class="reservation-actions">
                    ${canModify ? `
                        <button class="btn-warning" onclick="editReservation(${reservation.id})">Modifier</button>
                        <button class="btn-danger" onclick="cancelReservation(${reservation.id})">Annuler</button>
                    ` : ''}
                    ${isPast ? '<span class="text-muted">Réservation passée</span>' : ''}
                </div>
            </div>
        `;
    }).join('');
}

/**
 * Retourne le texte du statut en français
 */
function getStatusText(status) {
    const statusMap = {
        'Confirmed': 'Confirmée',
        'Pending': 'En attente',
        'Cancelled': 'Annulée'
    };
    return statusMap[status] || status;
}

/**
 * Charge les statistiques du client
 */
function loadStatistics() {
    if (allReservations.length === 0) {
        setTimeout(loadStatistics, 1000); // Réessayer après 1 seconde
        return;
    }

    // Total des réservations
    document.getElementById('total-reservations').textContent = allReservations.length;

    // Réservations à venir
    const upcomingReservations = allReservations.filter(r => {
        const date = new Date(r.date_time);
        return date > new Date() && r.status !== 'Cancelled';
    });
    document.getElementById('upcoming-reservations').textContent = upcomingReservations.length;

    // Table préférée
    const tableFrequency = {};
    allReservations.forEach(r => {
        if (r.status === 'Confirmed') {
            tableFrequency[r.table_id] = (tableFrequency[r.table_id] || 0) + 1;
        }
    });

    let favoriteTable = '-';
    let maxCount = 0;
    for (const [table, count] of Object.entries(tableFrequency)) {
        if (count > maxCount) {
            maxCount = count;
            favoriteTable = `Table ${table}`;
        }
    }
    document.getElementById('favorite-table').textContent = favoriteTable;

    // Membre depuis (simulé)
    document.getElementById('member-since').textContent = '2023';
}

/**
 * Initialise les gestionnaires d'événements
 */
function initializeEventHandlers() {
    // Formulaire de profil
    document.getElementById('profile-form').addEventListener('submit', handleProfileUpdate);

    // Formulaire de mot de passe
    document.getElementById('password-form').addEventListener('submit', handlePasswordChange);

    // Formulaire de modification de réservation
    document.getElementById('edit-reservation-form').addEventListener('submit', handleReservationEdit);

    // Déconnexion
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });
}

/**
 * Gère la mise à jour du profil
 */
async function handleProfileUpdate(e) {
    e.preventDefault();

    const name = document.getElementById('name').value.trim();
    const phone = document.getElementById('phone').value.trim();

    if (!name || !phone) {
        showMessage('profile-message', 'Veuillez remplir tous les champs.', 'error');
        return;
    }

    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=update_client', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                client_id: currentUser.client_id,
                name: name,
                phone: phone
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('profile-message', 'Profil mis à jour avec succès!', 'success');

            // Mettre à jour les données locales
            currentUser.name = name;
            localStorage.setItem('user', JSON.stringify(currentUser));
        } else {
            showMessage('profile-message', data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('profile-message', 'Erreur lors de la mise à jour du profil.', 'error');
    }
}

/**
 * Gère le changement de mot de passe
 */
async function handlePasswordChange(e) {
    e.preventDefault();

    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        showMessage('password-message', 'Veuillez remplir tous les champs.', 'error');
        return;
    }

    if (newPassword !== confirmPassword) {
        showMessage('password-message', 'Les nouveaux mots de passe ne correspondent pas.', 'error');
        return;
    }

    if (newPassword.length < 8) {
        showMessage('password-message', 'Le nouveau mot de passe doit contenir au moins 8 caractères.', 'error');
        return;
    }

    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=change_password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: currentUser.id,
                current_password: currentPassword,
                new_password: newPassword
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('password-message', 'Mot de passe changé avec succès!', 'success');
            document.getElementById('password-form').reset();
        } else {
            showMessage('password-message', data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('password-message', 'Erreur lors du changement de mot de passe.', 'error');
    }
}

/**
 * Gère la modification d'une réservation
 */
async function handleReservationEdit(e) {
    e.preventDefault();

    const reservationId = document.getElementById('edit-reservation-id').value;
    const date = document.getElementById('edit-date').value;
    const time = document.getElementById('edit-time').value;
    const tableId = document.getElementById('edit-table').value;

    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=update_reservation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reservation_id: reservationId,
                table_id: tableId,
                date: date,
                time: time,
                status: 'Confirmed'
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('edit-message', 'Réservation modifiée avec succès!', 'success');
            closeModal();
            loadReservations();
        } else {
            showMessage('edit-message', data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('edit-message', 'Erreur lors de la modification.', 'error');
    }
}

/**
 * Ouvre la modal de modification de réservation
 */
async function editReservation(reservationId) {
    const reservation = allReservations.find(r => r.id == reservationId);
    if (!reservation) return;

    const date = new Date(reservation.date_time);
    const formattedDate = date.toISOString().split('T')[0];
    const formattedTime = date.toTimeString().split(' ')[0].substring(0, 5);

    document.getElementById('edit-reservation-id').value = reservationId;
    document.getElementById('edit-date').value = formattedDate;
    document.getElementById('edit-time').value = formattedTime;

    // Charger les tables disponibles
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=check_availability', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                date: formattedDate,
                time: formattedTime,
                seats: 1
            })
        });

        const data = await response.json();

        if (data.success) {
            const tableSelect = document.getElementById('edit-table');
            tableSelect.innerHTML = data.tables.map(table =>
                `<option value="${table.id}" ${table.id == reservation.table_id ? 'selected' : ''}>
                    Table ${table.id} (${table.seats} places)
                </option>`
            ).join('');
        }
    } catch (error) {
        console.error('Erreur:', error);
    }

    document.getElementById('edit-reservation-modal').style.display = 'block';
}

/**
 * Annule une réservation
 */
async function cancelReservation(reservationId) {
    if (!confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')) {
        return;
    }

    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=cancel_reservation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reservation_id: reservationId
            })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('profile-message', 'Réservation annulée avec succès!', 'success');
            loadReservations();
            loadStatistics();
        } else {
            showMessage('profile-message', data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('profile-message', 'Erreur lors de l\'annulation.', 'error');
    }
}

/**
 * Filtre les réservations par statut
 */
function filterReservations() {
    const selectedStatus = document.getElementById('status-filter').value;

    if (selectedStatus === '') {
        displayReservations(allReservations);
    } else {
        const filteredReservations = allReservations.filter(r => r.status === selectedStatus);
        displayReservations(filteredReservations);
    }
}

/**
 * Ferme la modal
 */
function closeModal() {
    document.getElementById('edit-reservation-modal').style.display = 'none';
}

/**
 * Gère la déconnexion
 */
async function logout() {
    try {
        localStorage.removeItem('user');
        await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=logout', { method: 'POST' });
        window.location.href = '../../index.html';
    } catch (error) {
        console.error('Erreur lors de la déconnexion:', error);
        window.location.href = '../../index.html';
    }
}

/**
 * Affiche un message
 */
function showMessage(containerId, message, type) {
    const messageDiv = document.getElementById(containerId);
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';

    if (type === 'success') {
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
}

// Fermer la modal en cliquant à l'extérieur
window.onclick = function(event) {
    const modal = document.getElementById('edit-reservation-modal');
    if (event.target === modal) {
        closeModal();
    }
}
