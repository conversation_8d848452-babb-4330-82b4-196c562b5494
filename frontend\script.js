/**
 * Script principal pour la page d'accueil du système de réservation
 */

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si l'utilisateur est connecté
    checkUserSession();
    
    // Gestion du défilement fluide pour les liens d'ancrage
    setupSmoothScrolling();
    
    // Gestion de la navigation responsive
    setupResponsiveNavigation();
    
    // Animation des éléments au scroll
    setupScrollAnimations();
});

/**
 * Vérifie la session utilisateur et met à jour la navigation
 */
function checkUserSession() {
    const user = JSON.parse(localStorage.getItem('user'));
    const nav = document.querySelector('nav ul');
    
    if (user && nav) {
        // L'utilisateur est connecté, modifier la navigation
        const loginLink = nav.querySelector('a[href="view/login/login.html"]');
        const signupLink = nav.querySelector('a[href="view/signup/signup.html"]');
        
        if (loginLink) {
            loginLink.textContent = 'Tableau de bord';
            loginLink.href = getDashboardUrl(user.role);
        }
        
        if (signupLink) {
            signupLink.textContent = 'Déconnexion';
            signupLink.href = '#';
            signupLink.addEventListener('click', handleLogout);
        }
        
        // Mettre à jour les boutons CTA
        updateCTAButtons(user);
    }
}

/**
 * Retourne l'URL du tableau de bord selon le rôle
 */
function getDashboardUrl(role) {
    switch (role) {
        case 'Admin':
            return 'view/admin/dashboard.html';
        case 'Manager':
            return 'view/manager/dashboard.html';
        case 'Client':
        default:
            return 'view/reservation/reservation.html';
    }
}

/**
 * Met à jour les boutons d'appel à l'action
 */
function updateCTAButtons(user) {
    const ctaButtons = document.querySelectorAll('.cta-buttons a, .hero-content a');
    
    ctaButtons.forEach(button => {
        if (button.href.includes('login.html')) {
            button.textContent = 'Réserver maintenant';
            button.href = 'view/reservation/reservation.html';
        }
    });
}

/**
 * Gère la déconnexion de l'utilisateur
 */
async function handleLogout(event) {
    event.preventDefault();
    
    try {
        // Supprimer les données de session
        localStorage.removeItem('user');
        
        // Appeler l'API de déconnexion si disponible
        const response = await fetch('backend/server/user.php?action=logout', {
            method: 'POST'
        });
        
        // Recharger la page pour mettre à jour l'interface
        window.location.reload();
    } catch (error) {
        console.error('Erreur lors de la déconnexion:', error);
        // Même en cas d'erreur, supprimer les données locales et recharger
        localStorage.removeItem('user');
        window.location.reload();
    }
}

/**
 * Configure le défilement fluide pour les liens d'ancrage
 */
function setupSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(event) {
            event.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Configure la navigation responsive
 */
function setupResponsiveNavigation() {
    // Créer un bouton de menu mobile si nécessaire
    const header = document.querySelector('header .container');
    const nav = document.querySelector('nav');
    
    if (header && nav && window.innerWidth <= 768) {
        const menuToggle = document.createElement('button');
        menuToggle.className = 'menu-toggle';
        menuToggle.innerHTML = '☰';
        menuToggle.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            display: none;
        `;
        
        // Ajouter le bouton au header
        header.appendChild(menuToggle);
        
        // Gérer le clic sur le bouton
        menuToggle.addEventListener('click', function() {
            nav.classList.toggle('mobile-open');
        });
        
        // Afficher/masquer le bouton selon la taille d'écran
        function checkScreenSize() {
            if (window.innerWidth <= 768) {
                menuToggle.style.display = 'block';
                nav.style.display = nav.classList.contains('mobile-open') ? 'block' : 'none';
            } else {
                menuToggle.style.display = 'none';
                nav.style.display = 'block';
                nav.classList.remove('mobile-open');
            }
        }
        
        window.addEventListener('resize', checkScreenSize);
        checkScreenSize();
    }
}

/**
 * Configure les animations au scroll
 */
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Appliquer l'animation aux sections
    const sections = document.querySelectorAll('.about, .menu-preview, .reservation-cta');
    
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });
}

/**
 * Affiche un message de notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    // Couleurs selon le type
    const colors = {
        success: '#27ae60',
        error: '#e74c3c',
        warning: '#f39c12',
        info: '#3498db'
    };
    
    notification.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    // Animation d'entrée
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Suppression automatique
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

/**
 * Utilitaire pour formater les dates
 */
function formatDate(date) {
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    return new Date(date).toLocaleDateString('fr-FR', options);
}

/**
 * Utilitaire pour valider les emails
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Utilitaire pour valider les numéros de téléphone
 */
function isValidPhone(phone) {
    const phoneRegex = /^(\+33|0)[1-9](\d{8})$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}
